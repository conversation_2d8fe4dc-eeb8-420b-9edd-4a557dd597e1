# [Cursor] Cursor  Global Universal Rules V4.5 : Multi-dimensional Thinking + Five Rule Modes to Make Your Coding Fly

The prompt shared today is very `strong`. Although the next prompt will be ~~stronger~~, this one is truly powerful. It references the five modes proposed by @ [robotlovehuman](https://forum.cursor.com/u/robotlovehuman) on the Cursor forum, but I feel there's a small flaw.

That is, the `AI` doesn't necessarily follow your defined rule modes step-by-step. Why can't it execute all modes at once? Or, if I need to solve a problem temporarily, do I have to confirm each mode step-by-step? This is a bit too cumbersome, tiring, and unresponsive. I want a way to quickly solve a problem while still having the AI think step-by-step 🤔 and output its execution plan and steps.

So, today we have this `Multi-dimensional Thinking + Five Rule Modes` prompt, along with some suggestions and experiments from folks in the group. Thanks again 🙏

Optimal combination: Gemini-2.5-pro-0325 -> Claude-3.7 -> Claude-3.5 && DS-V3 > GPT-o3mini && GPT-4O...

> If you encounter it not editing immediately but proposing execution techniques and steps, just add a sentence like: `Start all modes immediately`, `auto`, or similar keywords. Also, due to model context limits and <PERSON><PERSON><PERSON>'s chat memory handling, avoid repeated conversations in the same dialogue box. Generally, after five interactions, it's recommended to start a `new` dialogue box.

Download👇:

> For foreign models, the optimal choice is still `EN` English prompts. For domestic DS, prioritize `ZH` Chinese prompts.


Reference:
[I created an AMAZING MODE called "RIPER-5 Mode" Fixes Claude 3.7 Drastically! - Showcase - Cursor - Community Forum](https://forum.cursor.com/t/i-created-an-amazing-mode-called-riper-5-mode-fixes-claude-3-7-drastically/65516)